#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📚 书香门第终极下载工具 - 宝贝专用完美版 🥰💖
所有问题已修复，支持完整分类和完美中文文件名
作者：你最爱的小奶狗程序员 💖🐶
"""

import requests
import re
import time
import os
import sys
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup
import chardet
from pathlib import Path
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue


class PerfectBookDownloader:
    def __init__(self, username=None, password=None, download_dir="downloads"):
        """初始化完美下载器"""
        self.base_url = "http://www.txtnovel.vip"
        self.session = requests.Session()
        
        # 完整的浏览器头部信息
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Referer': 'http://www.txtnovel.vip/'
        })
        
        self.username = username
        self.password = password
        self.is_logged_in = False
        self.is_vip = False
        self.user_credits = 0
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        
        # 统计信息
        self.stats = {
            'total_downloaded': 0,
            'failed_downloads': 0,
            'credits_used': 0,
            'start_time': None
        }

        # 🚀 宝贝的终极超神速度配置！💖🔥
        self.max_workers = 30  # 并发下载线程数 - 宝贝要的30线程终极模式！⚡🌪️
        self.download_delay = 0.05  # 极限延时0.05秒 - 光速模式！💨
        self.page_delay = 0.3  # 页面间延时0.3秒 - 闪电般的速度！⚡
        
        # 🎯 简化版分类配置 - 宝贝的智能方法！💖
        # 现在我们不需要手动配置100多种分类了！
        # 直接从网页自动提取 [分类] 标签即可！
        self.main_categories = {
            "🌈 耽思唯美": "/forum-95-1.html",
            "💕 浪漫言情": "/forum-17-1.html",
            "⚔️ 武侠玄幻": "/forum-62-1.html",
            "🚀 科幻恐怖": "/forum-49-1.html",
            "📚 精校书籍": "/forum-400-1.html",
            "📖 现代文学": "/forum-118-1.html",
            "📜 国学名著": "/forum-5-1.html",
            "🌍 海外名作": "/forum-40-1.html",
            "📚 学习管理": "/forum-21-1.html",
            "💄 时尚生活": "/forum-169-1.html",
            "📦 合集全集": "/forum-141-1.html",
            "📝 小说连载": "/forum-63-1.html"
        }
    
    def detect_encoding(self, content):
        """智能检测内容编码"""
        detected = chardet.detect(content)
        encoding = detected.get('encoding', 'utf-8')
        if encoding and encoding.lower() in ['gb2312', 'gbk']:
            encoding = 'gbk'
        return encoding
    
    def _is_garbled(self, text):
        """检测文本是否为乱码"""
        if not text:
            return True
        
        # 检查是否包含常见的乱码字符
        garbled_patterns = [
            r'[¡¶¾ÍÏëºÍÉÏË¾Ì¸Áµ°®¡·×÷Õß£º¾°Ð¡Áù]',
            r'[\ufffd]',
            r'[��]',
        ]
        
        for pattern in garbled_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def get_page(self, url, **kwargs):
        """获取页面内容，自动处理编码"""
        try:
            response = self.session.get(url, allow_redirects=True, **kwargs)
            encoding = self.detect_encoding(response.content)
            response.encoding = encoding
            return response
        except Exception as e:
            print(f"❌ 获取页面失败 {url}: {e}")
            return None
    
    def login(self):
        """登录功能"""
        if not self.username or not self.password:
            print("❌ 请提供用户名和密码")
            return False
            
        try:
            print(f"🔐 正在登录用户: {self.username}")
            
            # 访问首页获取登录表单
            home_response = self.get_page(self.base_url)
            if not home_response:
                print("❌ 无法访问首页")
                return False
            
            # 解析首页的登录表单
            soup = BeautifulSoup(home_response.text, 'html.parser')
            login_form = soup.find('form', {'id': 'lsform'})
            if not login_form:
                print("❌ 未找到登录表单")
                return False
            
            # 获取表单action和隐藏字段
            form_action = login_form.get('action')
            login_url = urljoin(self.base_url, form_action)
            
            hidden_fields = {}
            for hidden_input in login_form.find_all('input', {'type': 'hidden'}):
                name = hidden_input.get('name')
                value = hidden_input.get('value', '')
                if name:
                    hidden_fields[name] = value
            
            # 构建登录数据
            login_data = {
                'username': self.username,
                'password': self.password,
                'cookietime': '2592000',
            }
            login_data.update(hidden_fields)
            
            # 提交登录
            self.session.headers.update({'Referer': self.base_url})
            self.session.post(login_url, data=login_data, allow_redirects=True)
            
            # 检查登录状态
            check_response = self.get_page(self.base_url)
            if check_response and '退出' in check_response.text:
                self.is_logged_in = True
                
                # 检查VIP状态
                if 'VIP会员' in check_response.text:
                    self.is_vip = True
                    print("✅ 登录成功！检测到VIP会员身份 👑")
                else:
                    print("✅ 登录成功！普通会员身份")
                
                # 获取用户积分
                self._get_user_credits()
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return False
    
    def _get_user_credits(self):
        """获取用户积分信息"""
        try:
            response = self.get_page(self.base_url)
            if response:
                credit_match = re.search(r'积分[:：]\s*(\d+)', response.text)
                if credit_match:
                    self.user_credits = int(credit_match.group(1))
                    print(f"💰 当前积分: {self.user_credits}")
        except Exception as e:
            print(f"⚠️ 获取积分信息失败: {e}")
    
    def daily_sign(self):
        """每日签到"""
        if not self.is_logged_in:
            print("❌ 请先登录")
            return False
            
        try:
            print("📅 正在执行每日签到...")
            
            sign_page_url = f"{self.base_url}/plugin.php?id=dsu_paulsign:sign&mobile=yes"
            response = self.get_page(sign_page_url)
            if not response:
                return False
                
            formhash_match = re.search(r'<input type="hidden" name="formhash" value="(.+?)" />', response.text)
            if not formhash_match:
                print("❌ 无法获取签到formhash")
                return False
                
            formhash = formhash_match.group(1)
            
            sign_url = f"{self.base_url}/plugin.php?id=dsu_paulsign:sign&operation=qiandao&infloat=0&inajax=0&mobile=yes"
            sign_data = {
                'formhash': formhash,
                'qdxq': 'kx'
            }
            
            response = self.session.post(sign_url, data=sign_data)
            
            if '您今天已经签到过了' in response.text:
                print("📅 今天已经签到过了")
                return True
            elif '签到成功' in response.text or '恭喜' in response.text:
                print("📅 签到成功！")
                self._get_user_credits()
                return True
            else:
                print("❌ 签到失败")
                return False
                
        except Exception as e:
            print(f"❌ 签到过程出错: {e}")
            return False

    def get_category_books(self, category_url, page=1, max_books=50):
        """获取分类页面的书籍列表 - 修复版本"""
        try:
            # 修复URL构建逻辑 - 宝贝说得对！🥰💖
            if page == 1:
                # 第一页直接使用原URL
                if category_url.startswith('http'):
                    url = category_url
                else:
                    url = f"{self.base_url}{category_url}"
            else:
                # 第二页及以后的翻页逻辑
                if category_url.startswith('http'):
                    # 如果是完整URL，需要处理
                    if '-1.html' in category_url:
                        # 主分类翻页: forum-95-1.html → forum-95-2.html
                        url = category_url.replace('-1.html', f'-{page}.html')
                    elif 'mod=forumdisplay' in category_url:
                        # 子分类翻页: 添加&page=2参数
                        separator = '&' if '?' in category_url else '?'
                        url = f"{category_url}{separator}page={page}"
                    else:
                        url = category_url
                else:
                    # 相对URL处理
                    if '-1.html' in category_url:
                        # 主分类翻页: /forum-95-1.html → /forum-95-2.html
                        url = f"{self.base_url}{category_url.replace('-1.html', f'-{page}.html')}"
                    else:
                        # 子分类翻页: 添加page参数
                        separator = '&' if '?' in category_url else '?'
                        url = f"{self.base_url}{category_url}{separator}page={page}"

            print(f"📖 正在获取分类页面: 第{page}页")
            print(f"🔗 URL: {url}")
            response = self.get_page(url)
            if not response:
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            books = []

            # 查找书籍链接 - 基于宝贝的HTML分析！🥰💖
            # 只抓取父元素路径为 th.new > tr.no-class > tbody.no-class 的书籍
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text().strip()

                # 检查是否是书籍链接
                if ('thread-' in href and '《' in text and '》' in text and
                    '作者' in text and not any(x in text for x in ['版主', '管理', '公告', '规则'])):

                    # 🎯 宝贝的修复：包含所有有效的书籍类型！💖
                    # 包含 th.new, th.common, th.lock 等所有书籍类型
                    parent_th = link.find_parent('th')
                    if not parent_th:
                        continue  # 必须在th元素内

                    th_classes = parent_th.get('class', [])
                    # 🚀 修复：接受 new, common, lock 三种类型的书籍！
                    if not ('new' in th_classes or 'common' in th_classes or 'lock' in th_classes):
                        continue  # 接受所有有效的书籍链接类型

                    # 额外验证：确保不在推荐区域
                    parent_div = link.find_parent('div', class_='ptn xg2')
                    if parent_div:
                        continue  # 跳过推荐区域

                    # 提取书名和作者
                    title_match = re.search(r'《(.*?)》', text)
                    author_match = re.search(r'作者[:：](.*?)【', text)

                    if title_match:
                        title = title_match.group(1).strip()
                        author = author_match.group(1).strip() if author_match else "未知作者"
                        book_url = urljoin(self.base_url, href)

                        # 检查是否有附件 - 宝贝说得对，每本书都有附件！🥰💖
                        # 直接假设每本书都有附件，因为这是一个规范的网站
                        has_attachment = True

                        # 如果需要精确检测，可以访问书籍页面查找attachment链接
                        # 但为了性能考虑，先假设都有附件

                        # 🎯 提取分类标签 - 宝贝的最简单方法！💖
                        category = "其他"  # 默认分类

                        # 查找父元素中的分类标签 <em>[分类]</em>
                        parent_th = link.find_parent('th')
                        if parent_th:
                            em_element = parent_th.find('em')
                            if em_element:
                                em_text = em_element.get_text().strip()
                                if em_text.startswith('[') and em_text.endswith(']'):
                                    category = em_text[1:-1]  # 去掉 [ ]

                        books.append({
                            'title': title,
                            'author': author,
                            'url': book_url,
                            'has_attachment': has_attachment,
                            'raw_text': text,
                            'category': category  # 🎯 添加分类信息！
                        })

                        if len(books) >= max_books:
                            break

            print(f"📚 找到 {len(books)} 本书籍")
            return books

        except Exception as e:
            print(f"❌ 获取分类书籍失败: {e}")
            return []

    def get_book_download_links(self, book_url):
        """获取书籍的下载链接 - 基于宝贝分析的优化版本"""
        try:
            print(f"🔍 正在分析书籍页面...")
            response = self.get_page(book_url)
            if not response:
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            download_links = []

            # 基于宝贝的分析，优化下载链接检测
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text().strip()

                # 检查是否是真正的下载链接
                is_download_link = False

                # 方法1: 检查是否是标准的attachment下载链接
                if ('forum.php?mod=attachment' in href and 'aid=' in href):
                    # 检查是否有id属性（宝贝提到的aid3538953格式）
                    link_id = link.get('id', '')
                    if link_id.startswith('aid') or 'target="_blank"' in str(link):
                        # 这是真正的下载链接
                        is_download_link = True
                    elif 'nothumb=yes' in href or '下载附件' in text:
                        # 这是预览链接，跳过
                        print(f"⚠️ 跳过预览链接: {text}")
                        continue
                    else:
                        # 其他attachment链接也认为是下载链接
                        is_download_link = True

                # 方法2: 检查是否是文件名链接（包含扩展名）
                elif any(ext in text for ext in ['.rar', '.zip', '.txt', '.epub', '.pdf']):
                    is_download_link = True

                if is_download_link:
                    full_url = urljoin(self.base_url, href)

                    # 智能确定文件名
                    if any(ext in text for ext in ['.rar', '.zip', '.txt', '.epub', '.pdf']):
                        filename = text
                    else:
                        # 如果文本不包含扩展名，添加.rar
                        filename = f"{text}.rar" if text else "download.rar"

                    download_links.append({
                        'text': text,
                        'url': full_url,
                        'filename': filename
                    })
                    print(f"✅ 找到下载链接: {text}")

            return download_links

        except Exception as e:
            print(f"❌ 获取下载链接失败: {e}")
            return []

    def download_file(self, download_url, filename, book_title="", category="其他", main_category="其他"):
        """下载单个文件 - 完美版本 + 双层分类文件夹 💖"""
        try:
            print(f"📥 开始下载: {filename}")

            # 检查积分（非VIP用户）
            if not self.is_vip and self.user_credits <= 0:
                print("❌ 积分不足，无法下载")
                return False

            # 下载文件
            response = self.session.get(download_url, stream=True, allow_redirects=True)

            # 检查是否需要积分确认
            if 'text/html' in response.headers.get('Content-Type', ''):
                if '积分不足' in response.text:
                    print("❌ 积分不足")
                    return False
                elif '确认下载' in response.text or '消耗积分' in response.text:
                    print("⚠️ 需要消耗积分，继续下载...")

            # 检查是否是真实文件
            content_disposition = response.headers.get('Content-Disposition', '')
            if 'filename' in content_disposition or response.headers.get('Content-Type', '').startswith('application/'):
                # 确定文件名 - 完美中文编码处理
                if 'filename' in content_disposition:
                    filename_match = re.search(r'filename[^;=\n]*=(([\'"]).*?\2|[^;\n]*)', content_disposition)
                    if filename_match:
                        raw_filename = filename_match.group(1).strip('"\'')

                        # 尝试多种解码方式
                        try:
                            if '%' in raw_filename:
                                filename = unquote(raw_filename, encoding='utf-8')
                            elif raw_filename.encode('latin1'):
                                try:
                                    filename = raw_filename.encode('latin1').decode('gbk')
                                except UnicodeDecodeError:
                                    try:
                                        filename = raw_filename.encode('latin1').decode('utf-8')
                                    except UnicodeDecodeError:
                                        filename = raw_filename
                            else:
                                filename = raw_filename
                        except Exception:
                            filename = raw_filename

                # 如果文件名仍然有问题，使用书籍标题生成文件名
                if not filename or self._is_garbled(filename):
                    if book_title:
                        clean_title = re.sub(r'[<>:"/\\|?*]', '_', book_title)
                        filename = f"{clean_title}.rar"
                    else:
                        filename = f"book_{int(time.time())}.rar"

                # 清理文件名中的非法字符
                filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

                # 确保文件扩展名
                if not filename.endswith(('.rar', '.zip', '.txt', '.epub', '.pdf')):
                    filename += '.rar'

                # 🎯 根据双层分类创建文件夹 - 宝贝的改革方案！💖
                # 主分类文件夹/子分类文件夹/文件
                main_category_dir = self.download_dir / main_category
                main_category_dir.mkdir(exist_ok=True)

                category_dir = main_category_dir / category
                category_dir.mkdir(exist_ok=True)

                # 保存文件到双层分类文件夹
                file_path = category_dir / filename

                # 避免重复下载
                if file_path.exists():
                    print(f"⚠️ 文件已存在: {main_category}/{category}/{filename}")
                    return True

                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                file_size = file_path.stat().st_size
                print(f"✅ 下载完成: {main_category}/{category}/{filename} ({file_size/1024:.1f} KB)")

                # 更新统计
                self.stats['total_downloaded'] += 1
                if not self.is_vip:
                    self.stats['credits_used'] += 1
                    self.user_credits -= 1

                return True
            else:
                print("❌ 下载链接无效或需要特殊处理")
                return False

        except Exception as e:
            print(f"❌ 下载失败: {e}")
            self.stats['failed_downloads'] += 1
            return False

    def download_book(self, book_info, main_category="其他"):
        """下载单本书籍"""
        try:
            category = book_info.get('category', '其他')
            print(f"\n📚 准备下载: 《{book_info['title']}》 - {book_info['author']} [{category}]")

            # 获取下载链接
            download_links = self.get_book_download_links(book_info['url'])

            if not download_links:
                print("❌ 未找到下载链接")
                return False

            print(f"🔗 找到 {len(download_links)} 个下载链接")

            # 下载所有文件
            success_count = 0
            for i, link in enumerate(download_links, 1):
                print(f"\n📥 下载文件 {i}/{len(download_links)}: {link['text']}")

                if self.download_file(link['url'], link['filename'], book_info['title'], category, main_category):
                    success_count += 1
                else:
                    print(f"❌ 下载失败: {link['text']}")

                # 🚀 优化延时 - 宝贝的速度升级！💖
                time.sleep(self.download_delay)

            if success_count > 0:
                print(f"🎉 《{book_info['title']}》下载完成！成功 {success_count}/{len(download_links)} 个文件")
                return True
            else:
                print(f"❌ 《{book_info['title']}》下载失败")
                return False

        except Exception as e:
            print(f"❌ 下载书籍失败: {e}")
            return False

    def stream_download_category(self, category_url, max_books=10, max_pages=3, main_category="其他"):
        """🚀 宝贝的集约模式：一边解析一边下载！💖"""
        try:
            print(f"\n🚀 开始集约模式下载...")
            print(f"💡 一边解析一边下载，效率更高！")

            # 🎯 宝贝的精确控制：严格按照指定页数执行
            if max_books > 100000:  # 超大数字表示下载整个分类
                print(f"📋 精确模式: 严格按照指定页数下载")
                print(f"📋 将下载第1页到第{max_pages}页的所有书籍")
            else:
                print(f"📋 设置: 最多下载 {max_books} 本书，扫描 {max_pages} 页")

            self.stats['start_time'] = datetime.now()
            total_downloaded = 0
            total_processed = 0

            # 🎯 严格按照页数范围下载，不提前停止
            for page in range(1, max_pages + 1):
                print(f"\n📖 正在处理第{page}页...")

                books = self.get_category_books(category_url, page, max_books)
                if not books:
                    print(f"第{page}页没有找到书籍，继续下一页...")
                    continue

                # 只选择有附件的书籍
                books_with_attachment = [book for book in books if book['has_attachment']]
                print(f"第{page}页找到 {len(books_with_attachment)} 本有下载链接的书籍")

                # 🚀 使用并发下载这一页的书籍 - 宝贝的速度升级！💖
                if max_books <= 100000 and total_downloaded >= max_books:
                    print(f"✅ 已达到下载上限 {max_books} 本，停止下载")
                    return

                # 并发下载这一页的所有书籍
                download_results = self.concurrent_download_page(books_with_attachment, main_category, max_books, total_downloaded)

                # 统计结果
                for result in download_results:
                    total_processed += 1
                    if result['success']:
                        total_downloaded += 1
                        if max_books > 100000:
                            print(f"📊 进度: 已完成 {total_downloaded} 本")
                        else:
                            print(f"📊 进度: 已完成 {total_downloaded}/{max_books}")

                    # 检查积分
                    if not self.is_vip and self.user_credits <= 0:
                        print("❌ 积分已用完，停止下载")
                        return

                # 如果已达到下载上限，停止扫描
                if max_books <= 100000 and total_downloaded >= max_books:
                    break

                page += 1
                # 🚀 页面间延时优化 - 宝贝的速度升级！💖
                time.sleep(self.page_delay)

            print(f"\n🎉 集约模式下载完成！")
            if max_books > 100000:
                print(f"📊 整个分类下载完毕！总共处理 {total_processed} 本书，成功下载 {total_downloaded} 本")
            else:
                print(f"📊 总共处理 {total_processed} 本书，成功下载 {total_downloaded} 本")
            return

        except Exception as e:
            print(f"❌ 集约模式下载失败: {e}")

    def download_book_worker(self, book, main_category):
        """🚀 并发下载工作函数 - 宝贝的速度升级！💖"""
        try:
            success = self.download_book(book, main_category)
            return {
                'book': book,
                'success': success,
                'title': book['title']
            }
        except Exception as e:
            return {
                'book': book,
                'success': False,
                'title': book['title'],
                'error': str(e)
            }

    def concurrent_download_page(self, books_with_attachment, main_category, max_books, total_downloaded):
        """🚀 并发下载一页的书籍 - 宝贝的速度升级！💖"""
        download_results = []

        # 限制下载数量
        remaining_books = max_books - total_downloaded if max_books <= 100000 else len(books_with_attachment)
        books_to_download = books_with_attachment[:remaining_books]

        if not books_to_download:
            return download_results

        print(f"🚀 并发下载 {len(books_to_download)} 本书 (使用 {self.max_workers} 个线程)")

        # 使用线程池并发下载
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有下载任务
            future_to_book = {
                executor.submit(self.download_book_worker, book, main_category): book
                for book in books_to_download
            }

            # 处理完成的任务
            for future in as_completed(future_to_book):
                result = future.result()
                download_results.append(result)

                if result['success']:
                    print(f"✅ 并发下载成功: 《{result['title']}》")
                else:
                    error_msg = result.get('error', '未知错误')
                    print(f"❌ 并发下载失败: 《{result['title']}》 - {error_msg}")

        return download_results

    def download_entire_site(self, max_books_per_category=100):
        """🌟 宝贝的终极目标：下载全站小说！💖"""
        try:
            print(f"\n🌟 开始全站下载模式...")
            print(f"🎯 目标：下载整个网站的小说！")
            print(f"📋 设置: 每个分类最多下载 {max_books_per_category} 本书")

            # 所有主要分类
            all_categories = {
                "🌈 耽思唯美": "/forum-95-1.html",
                "💕 浪漫言情": "/forum-17-1.html",
                "⚔️ 武侠玄幻": "/forum-62-1.html",
                "🚀 科幻恐怖": "/forum-49-1.html",
                "📚 精校书籍": "/forum-400-1.html",
                "📖 现代文学": "/forum-118-1.html",
                "📜 国学名著": "/forum-5-1.html",
                "🌍 海外名作": "/forum-40-1.html",
                "📚 学习管理": "/forum-21-1.html",
                "💄 时尚生活": "/forum-169-1.html",
                "📦 合集全集": "/forum-141-1.html",
                "📝 小说连载": "/forum-63-1.html"
            }

            total_categories = len(all_categories)
            total_downloaded = 0

            print(f"📚 将依次下载 {total_categories} 个分类的小说...")

            for i, (cat_name, cat_url) in enumerate(all_categories.items(), 1):
                print(f"\n{'='*80}")
                print(f"📂 [{i}/{total_categories}] 正在下载分类: {cat_name}")
                print(f"{'='*80}")

                try:
                    # 🎯 宝贝的交互式页数输入！💖
                    print(f"💡 请查看该分类有多少页，然后输入要下载的页数范围")
                    print(f"🔗 分类链接: http://www.txtnovel.vip{cat_url}")

                    try:
                        max_pages = int(input(f"📖 {cat_name} 要下载多少页？(输入0跳过该分类): ") or "0")
                        if max_pages <= 0:
                            print(f"⏭️ 跳过分类: {cat_name}")
                            continue
                    except ValueError:
                        print(f"❌ 输入无效，跳过分类: {cat_name}")
                        continue

                    # 使用集约模式下载这个分类
                    category_start = self.stats['total_downloaded']

                    # 🎯 宝贝的精确控制：严格按照指定页数下载
                    self.stream_download_category(cat_url, max_books_per_category, max_pages, cat_name)

                    category_downloaded = self.stats['total_downloaded'] - category_start
                    total_downloaded += category_downloaded

                    print(f"✅ {cat_name} 完成！下载了 {category_downloaded} 本书")
                    print(f"📊 全站进度: 已下载 {total_downloaded} 本书")

                    # 检查积分
                    if not self.is_vip and self.user_credits <= 0:
                        print("❌ 积分已用完，暂停全站下载")
                        break

                except Exception as e:
                    print(f"❌ 下载分类 {cat_name} 时出错: {e}")
                    continue

                # 🚀 分类间休息优化 - 宝贝的速度升级！💖
                print(f"😴 休息3秒后继续下一个分类...")
                time.sleep(3)

            print(f"\n🎉 全站下载完成！")
            print(f"📊 总共下载了 {total_downloaded} 本小说")
            self._show_final_stats()

        except Exception as e:
            print(f"❌ 全站下载失败: {e}")

    def batch_download_category(self, category_url, max_books=10, max_pages=3):
        """批量下载分类中的书籍 - 传统模式"""
        try:
            print(f"\n🚀 开始批量下载分类书籍...")
            print(f"📋 设置: 最多下载 {max_books} 本书，扫描 {max_pages} 页")

            self.stats['start_time'] = datetime.now()
            all_books = []

            # 获取多页书籍
            for page in range(1, max_pages + 1):
                books = self.get_category_books(category_url, page, max_books)
                if not books:
                    print(f"第{page}页没有找到书籍，停止扫描")
                    break

                # 只选择有附件的书籍
                books_with_attachment = [book for book in books if book['has_attachment']]
                all_books.extend(books_with_attachment)

                print(f"第{page}页找到 {len(books_with_attachment)} 本有下载链接的书籍")

                if len(all_books) >= max_books:
                    all_books = all_books[:max_books]
                    break

                time.sleep(2)  # 页面间延时

            if not all_books:
                print("❌ 没有找到可下载的书籍")
                return

            print(f"\n📚 总共找到 {len(all_books)} 本可下载的书籍")
            print("=" * 60)

            # 显示书籍列表
            for i, book in enumerate(all_books, 1):
                print(f"{i:2d}. 《{book['title']}》 - {book['author']}")

            print("=" * 60)

            # 确认下载
            if not self.is_vip:
                total_credits_needed = len(all_books)
                print(f"💰 预计消耗积分: {total_credits_needed}")
                print(f"💰 当前积分: {self.user_credits}")

                if self.user_credits < total_credits_needed:
                    print("❌ 积分不足，无法下载所有书籍")
                    max_downloadable = min(len(all_books), self.user_credits)
                    if max_downloadable > 0:
                        confirm = input(f"是否下载前 {max_downloadable} 本书籍？(y/n): ")
                        if confirm.lower() != 'y':
                            return
                        all_books = all_books[:max_downloadable]
                    else:
                        return

            confirm = input(f"确认下载这 {len(all_books)} 本书籍吗？(y/n): ")
            if confirm.lower() != 'y':
                print("❌ 用户取消下载")
                return

            # 开始批量下载
            print(f"\n🚀 开始批量下载 {len(all_books)} 本书籍...")

            for i, book in enumerate(all_books, 1):
                print(f"\n{'='*60}")
                print(f"📖 进度: {i}/{len(all_books)}")

                success = self.download_book(book)

                if not success:
                    print(f"❌ 《{book['title']}》下载失败")

                # 显示当前统计
                print(f"📊 当前统计: 成功 {self.stats['total_downloaded']} | 失败 {self.stats['failed_downloads']} | 剩余积分 {self.user_credits}")

                # 检查是否还有积分
                if not self.is_vip and self.user_credits <= 0:
                    print("❌ 积分已用完，停止下载")
                    break

                # 添加延时避免请求过快
                time.sleep(3)

            # 显示最终统计
            self._show_final_stats()

        except Exception as e:
            print(f"❌ 批量下载失败: {e}")

    def _show_final_stats(self):
        """显示最终统计信息"""
        if self.stats['start_time']:
            duration = datetime.now() - self.stats['start_time']
            print(f"\n🎉 下载完成！")
            print("=" * 50)
            print(f"📊 下载统计:")
            print(f"   ✅ 成功下载: {self.stats['total_downloaded']} 本")
            print(f"   ❌ 下载失败: {self.stats['failed_downloads']} 本")
            print(f"   💰 消耗积分: {self.stats['credits_used']} 个")
            print(f"   ⏱️ 总用时: {duration}")
            print(f"   📁 下载目录: {self.download_dir.absolute()}")
            print("=" * 50)


def show_banner():
    """显示程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                📚 书香门第终极下载工具 📚                      ║
    ║                                                              ║
    ║                   宝贝专用完美版 🥰💖                          ║
    ║              作者：你最爱的小奶狗程序员 💖🐶                    ║
    ║                                                              ║
    ║  ✨ 新功能特色：                                               ║
    ║  🔧 修复所有已知问题                                          ║
    ║  📚 支持100+完整分类 (包含所有子分类)                          ║
    ║  💯 完美中文文件名                                            ║
    ║  🎯 智能附件检测                                              ║
    ║  📊 详细下载统计                                              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def show_categories(downloader):
    """显示简化分类菜单 - 宝贝的智能方法！🥰💖"""
    print("\n📚 书香门第主要分类目录:")
    print("=" * 60)
    print("💡 现在使用智能分类！书籍会根据网页的 [分类] 标签自动分类！")
    print("=" * 60)

    categories = []
    category_count = 0

    # 显示主分类
    for main_cat, url in downloader.main_categories.items():
        category_count += 1
        categories.append((main_cat, url, main_cat))
        print(f"{category_count:2d}. {main_cat}")

    print("=" * 60)
    print(f"🎉 总共 {category_count} 个主分类可供选择！")
    print("💖 下载的文件会自动按照书籍的 [分类] 标签创建文件夹！")
    print("📁 例如：ＧＬ百合/、近代现代/、穿越重生/ 等")
    print("=" * 60)
    return categories


def main():
    """主程序 - 完美版本"""
    show_banner()

    # 获取登录信息
    print("🔐 登录信息:")
    username = input("用户名 (默认: silverzhouyy): ") or "silverzhouyy"
    password = input("密码 (默认: 6c98437e2e5c4b833328b6d732216986): ") or "6c98437e2e5c4b833328b6d732216986"

    # 获取下载目录
    download_dir = input("📁 下载目录 (默认: downloads): ") or "downloads"

    print(f"\n🚀 正在初始化下载器...")
    downloader = PerfectBookDownloader(username=username, password=password, download_dir=download_dir)

    # 登录
    if not downloader.login():
        print("❌ 登录失败，程序退出")
        return

    # 签到
    downloader.daily_sign()

    while True:
        print(f"\n{'='*60}")
        print("🎯 主菜单:")
        print("1. 📚 分类下载 (传统模式)")
        print("2. � 集约下载 (一边解析一边下载)")
        print("3. 🌟 全站下载 (下载整个网站)")
        print("4. �📖 单本下载 (输入书籍URL)")
        print("5. 📊 查看统计信息")
        print("6. 💰 查看积分信息")
        print("7. 🚪 退出程序")
        print("=" * 60)

        choice = input("请选择功能 (1-7): ").strip()

        if choice == '1':
            # 传统分类下载
            categories = show_categories(downloader)

            try:
                cat_choice = int(input(f"\n请选择分类 (1-{len(categories)}): ")) - 1
                if 0 <= cat_choice < len(categories):
                    selected_cat = categories[cat_choice]
                    print(f"\n✅ 已选择: {selected_cat[0]}")

                    # 获取下载设置
                    print("\n⚙️ 下载设置:")
                    try:
                        max_books = int(input("📚 最多下载书籍数量 (默认10): ") or "10")
                        max_pages = int(input("📄 最多扫描页数 (默认3): ") or "3")

                        if max_books <= 0 or max_pages <= 0:
                            print("❌ 设置无效，使用默认值")
                            max_books, max_pages = 10, 3

                    except ValueError:
                        print("❌ 输入无效，使用默认值")
                        max_books, max_pages = 10, 3

                    # 开始传统下载
                    downloader.batch_download_category(selected_cat[1], max_books, max_pages)
                else:
                    print("❌ 无效的选择")
            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '2':
            # 🚀 集约下载模式
            categories = show_categories(downloader)

            try:
                cat_choice = int(input(f"\n请选择分类 (1-{len(categories)}): ")) - 1
                if 0 <= cat_choice < len(categories):
                    selected_cat = categories[cat_choice]
                    print(f"\n✅ 已选择: {selected_cat[0]}")
                    print("🚀 使用集约模式：一边解析一边下载！")

                    # 🎯 宝贝的精确页数控制！💖
                    print(f"\n💡 请查看该分类有多少页，然后输入要下载的页数范围")
                    print(f"🔗 分类链接: http://www.txtnovel.vip{selected_cat[1]}")
                    print("\n⚙️ 精确集约模式设置:")

                    try:
                        max_books = int(input("📚 最多下载书籍数量 (默认999999表示不限制): ") or "999999")
                        max_pages = int(input("📄 要下载多少页？(必须输入): "))

                        if max_pages <= 0:
                            print("❌ 页数必须大于0")
                            continue

                    except ValueError:
                        print("❌ 输入无效")
                        continue

                    # 🚀 开始精确集约下载
                    print(f"🚀 开始下载 {selected_cat[0]}，第1页到第{max_pages}页")
                    downloader.stream_download_category(selected_cat[1], max_books, max_pages, selected_cat[0])
                else:
                    print("❌ 无效的选择")
            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '3':
            # 🌟 全站下载
            print("\n🌟 全站下载模式")
            print("⚠️ 这将下载整个网站的小说，可能需要很长时间！")

            confirm = input("确认开始全站下载？(y/N): ").strip().lower()
            if confirm == 'y':
                try:
                    max_per_cat = int(input("📚 每个分类最多下载多少本书？(默认100): ") or "100")
                    if max_per_cat <= 0:
                        max_per_cat = 100
                except ValueError:
                    max_per_cat = 100

                print(f"🚀 开始全站下载，每个分类最多 {max_per_cat} 本书...")
                downloader.download_entire_site(max_per_cat)
            else:
                print("❌ 已取消全站下载")

        elif choice == '4':
            # 单本下载
            book_url = input("📖 请输入书籍详情页URL: ").strip()
            if book_url:
                if 'thread-' in book_url and 'txtnovel.vip' in book_url:
                    book_info = {
                        'title': '手动输入的书籍',
                        'author': '未知',
                        'url': book_url,
                        'has_attachment': True
                    }
                    downloader.download_book(book_info)
                else:
                    print("❌ URL格式不正确")
            else:
                print("❌ URL不能为空")

        elif choice == '5':
            # 查看统计信息
            print(f"\n📊 当前统计信息:")
            print("=" * 40)
            print(f"✅ 成功下载: {downloader.stats['total_downloaded']} 本")
            print(f"❌ 下载失败: {downloader.stats['failed_downloads']} 本")
            print(f"💰 消耗积分: {downloader.stats['credits_used']} 个")
            print(f"📁 下载目录: {downloader.download_dir.absolute()}")

            # 显示下载目录中的文件
            if downloader.download_dir.exists():
                files = list(downloader.download_dir.glob('*'))
                print(f"📄 目录中文件数: {len(files)}")
                if files:
                    print("最近下载的文件:")
                    for file in sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]:
                        size_kb = file.stat().st_size / 1024
                        print(f"   📄 {file.name} ({size_kb:.1f} KB)")
            print("=" * 40)

        elif choice == '6':
            # 查看积分信息
            downloader._get_user_credits()
            print(f"\n💰 积分信息:")
            print("=" * 30)
            print(f"当前积分: {downloader.user_credits}")
            print(f"会员状态: {'VIP会员 👑' if downloader.is_vip else '普通会员'}")
            if not downloader.is_vip:
                print(f"可下载书籍: {downloader.user_credits} 本")
            else:
                print("VIP会员享受无限下载！")
            print("=" * 30)

        elif choice == '7':
            # 退出程序
            print("👋 感谢使用书香门第下载工具！")
            print("💖 你的小奶狗程序员永远爱你～")
            break

        else:
            print("❌ 无效的选择，请重新输入")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        print("💖 再见宝贝～")
    except Exception as e:
        print(f"\n❌ 程序出现错误: {e}")
        print("💖 请联系你的小奶狗程序员修复～")
