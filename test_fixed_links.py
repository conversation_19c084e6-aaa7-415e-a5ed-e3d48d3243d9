#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 测试修复后的下载链接检测 - 宝贝的验证工具 💖
"""

import requests
import re
import time
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import chardet

class LinkTester:
    def __init__(self):
        """初始化测试器"""
        self.base_url = "http://www.txtnovel.vip"
        self.session = requests.Session()
        
        # 完整的浏览器头部信息
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Referer': 'http://www.txtnovel.vip/'
        })
        
        self.is_logged_in = False
    
    def detect_encoding(self, content):
        """智能检测内容编码"""
        detected = chardet.detect(content)
        encoding = detected.get('encoding', 'utf-8')
        if encoding and encoding.lower() in ['gb2312', 'gbk']:
            encoding = 'gbk'
        return encoding
    
    def get_page(self, url, **kwargs):
        """获取页面内容，自动处理编码"""
        try:
            response = self.session.get(url, allow_redirects=True, **kwargs)
            encoding = self.detect_encoding(response.content)
            response.encoding = encoding
            return response
        except Exception as e:
            print(f"❌ 获取页面失败 {url}: {e}")
            return None
    
    def login(self):
        """登录功能"""
        username = "silverzhouyy"
        password = "6c98437e2e5c4b833328b6d732216986"
            
        try:
            print(f"🔐 正在登录用户: {username}")
            
            # 访问首页获取登录表单
            home_response = self.get_page(self.base_url)
            if not home_response:
                print("❌ 无法访问首页")
                return False
            
            # 解析首页的登录表单
            soup = BeautifulSoup(home_response.text, 'html.parser')
            login_form = soup.find('form', {'id': 'lsform'})
            if not login_form:
                print("❌ 未找到登录表单")
                return False
            
            # 获取表单action和隐藏字段
            form_action = login_form.get('action')
            login_url = urljoin(self.base_url, form_action)
            
            hidden_fields = {}
            for hidden_input in login_form.find_all('input', {'type': 'hidden'}):
                name = hidden_input.get('name')
                value = hidden_input.get('value', '')
                if name:
                    hidden_fields[name] = value
            
            # 构建登录数据
            login_data = {
                'username': username,
                'password': password,
                'cookietime': '2592000',
            }
            login_data.update(hidden_fields)
            
            # 提交登录
            self.session.headers.update({'Referer': self.base_url})
            self.session.post(login_url, data=login_data, allow_redirects=True)
            
            # 检查登录状态
            check_response = self.get_page(self.base_url)
            if check_response and '退出' in check_response.text:
                self.is_logged_in = True
                print("✅ 登录成功！")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return False
    
    def get_book_download_links_fixed(self, book_url):
        """修复后的下载链接检测"""
        try:
            print(f"🔍 正在分析书籍页面...")
            response = self.get_page(book_url)
            if not response:
                return []

            soup = BeautifulSoup(response.text, 'html.parser')
            download_links = []

            # 🎯 精确检测下载链接 - 排除VIP广告干扰！
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text().strip()
                link_id = link.get('id', '')

                # 🚀 关键修复：只接受真正的书籍下载链接！
                is_valid_download = False

                # 条件1: 必须是attachment链接且有aid ID
                if ('forum.php?mod=attachment' in href and 'aid=' in href and link_id.startswith('aid')):
                    # 排除预览链接
                    if 'nothumb=yes' not in href:
                        is_valid_download = True
                    else:
                        print(f"⚠️ 跳过预览链接: {text}")
                        continue

                # 条件2: 文本必须包含书籍特征（书名.rar格式）
                elif (any(ext in text for ext in ['.rar', '.zip', '.txt', '.epub', '.pdf']) and 
                      '《' in text and '》' in text and '作者' in text):
                    is_valid_download = True

                # 🛡️ 安全过滤：排除所有干扰链接
                if is_valid_download:
                    # 排除VIP广告链接
                    if any(keyword in text for keyword in [
                        'VIP用户组', '赞助成为', '免金币', '还在为金币不够而烦恼', 
                        'VIP会员', '加入VIP', '下载免金币'
                    ]):
                        print(f"⚠️ 跳过VIP广告: {text}")
                        continue
                    
                    # 排除导航链接
                    if any(keyword in text for keyword in [
                        'TXT全集下载', '浪漫言情', '耽思唯美', '武侠玄幻'
                    ]):
                        print(f"⚠️ 跳过导航链接: {text}")
                        continue
                    
                    # 排除其他干扰链接
                    if any(keyword in href for keyword in [
                        'vip.php', 'forumdisplay', 'forum.php?mod=viewthread'
                    ]):
                        print(f"⚠️ 跳过其他链接: {text}")
                        continue

                    # ✅ 通过所有检查，这是有效的下载链接
                    full_url = urljoin(self.base_url, href)

                    # 智能确定文件名
                    if any(ext in text for ext in ['.rar', '.zip', '.txt', '.epub', '.pdf']):
                        filename = text
                    else:
                        filename = f"{text}.rar" if text else "download.rar"

                    download_links.append({
                        'text': text,
                        'url': full_url,
                        'filename': filename
                    })
                    print(f"✅ 找到有效下载链接: {text}")

            return download_links

        except Exception as e:
            print(f"❌ 获取下载链接失败: {e}")
            return []

def main():
    """主程序"""
    print("🔧 测试修复后的下载链接检测！💖")
    
    tester = LinkTester()
    
    # 登录
    if not tester.login():
        print("❌ 登录失败，程序退出")
        return
    
    # 测试几个书籍的下载链接
    test_books = [
        ("第720页书籍", "http://www.txtnovel.vip/thread-4076343-1-720.html"),
        ("第722页书籍1", "http://www.txtnovel.vip/thread-4076065-1-722.html"),
        ("第722页书籍2", "http://www.txtnovel.vip/thread-4076033-1-722.html"),
    ]
    
    for name, book_url in test_books:
        print(f"\n{'='*80}")
        print(f"🔍 测试: {name}")
        print(f"🔗 URL: {book_url}")
        print("=" * 80)
        
        links = tester.get_book_download_links_fixed(book_url)
        
        print(f"\n📊 结果: 找到 {len(links)} 个有效下载链接")
        for i, link in enumerate(links, 1):
            print(f"   {i}. {link['text']}")
        
        time.sleep(2)
    
    print(f"\n🎉 测试完成！")
    print("💖 现在应该只显示真正的书籍下载链接了～")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出现错误: {e}")
