#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 测试修复后的原始版本下载器 - 宝贝的验证工具 💖
"""

import sys
sys.path.append('.')

# 导入修复后的原始下载器
from final_book_downloader import PerfectBookDownloader

def test_fixed_downloader():
    """测试修复后的下载器"""
    print("🔧 测试修复后的原始版本下载器！💖")
    
    # 初始化下载器
    downloader = PerfectBookDownloader(
        username="silverzhouyy",
        password="6c98437e2e5c4b833328b6d732216986",
        download_dir="test_downloads"
    )
    
    # 登录
    if not downloader.login():
        print("❌ 登录失败，程序退出")
        return
    
    # 测试几个书籍的下载链接
    test_books = [
        ("第720页书籍", "http://www.txtnovel.vip/thread-4076343-1-720.html"),
        ("第722页书籍1", "http://www.txtnovel.vip/thread-4076065-1-722.html"),
        ("第722页书籍2", "http://www.txtnovel.vip/thread-4076033-1-722.html"),
    ]
    
    for name, book_url in test_books:
        print(f"\n{'='*80}")
        print(f"🔍 测试: {name}")
        print(f"🔗 URL: {book_url}")
        print("=" * 80)
        
        links = downloader.get_book_download_links(book_url)
        
        print(f"\n📊 结果: 找到 {len(links)} 个有效下载链接")
        for i, link in enumerate(links, 1):
            print(f"   {i}. {link['text']}")
        
        # 如果有链接，测试下载第一个
        if links:
            print(f"\n🔧 测试下载第一个文件...")
            first_link = links[0]
            
            # 创建测试书籍信息
            book_info = {
                'title': f"测试书籍_{name}",
                'author': "测试作者",
                'url': book_url,
                'has_attachment': True,
                'category': '测试分类'
            }
            
            # 测试下载
            success = downloader.download_book(book_info, "测试分类")
            if success:
                print(f"✅ 下载测试成功！")
            else:
                print(f"❌ 下载测试失败")
        
        print(f"\n⏱️ 等待2秒后测试下一个...")
        import time
        time.sleep(2)
    
    print(f"\n🎉 测试完成！")
    print("💖 现在应该只显示真正的书籍下载链接了～")

if __name__ == "__main__":
    try:
        test_fixed_downloader()
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出现错误: {e}")
        import traceback
        traceback.print_exc()
